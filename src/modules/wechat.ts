/**
 * 微信SDK初始化插件
 */
import { defineVuePlugin } from '@/utils/helper'
import { useWechatStore } from '@/store/wechat'
import { isWechatBrowser } from '@/utils/wechat'
import { isUserLoggedIn } from '@/components/QrScanner/wechat'

export default defineVuePlugin((app) => {
  // 在应用启动时初始化微信SDK
  if (typeof window !== 'undefined' && isWechatBrowser()) {
    // 延迟初始化，确保DOM已加载，并检查用户登录状态
    setTimeout(() => {
      // 只有在用户已登录的情况下才初始化微信SDK
      if (isUserLoggedIn()) {
        const wechatStore = useWechatStore()
        wechatStore.initializeWechatSDK().catch(error => {
          console.warn('微信SDK初始化失败:', error)
        })
      } else {
        console.log('用户未登录，跳过微信SDK自动初始化')
      }
    }, 100)
  }
})
