<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录扫码测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        button.primary {
            background-color: #007bff;
            color: white;
        }
        button.success {
            background-color: #28a745;
            color: white;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <h1>登录扫码状态测试</h1>
    
    <div class="test-section">
        <h2>测试说明</h2>
        <p>这个页面用于测试登录成功后扫码按钮状态是否正确更新。</p>
        <p>测试步骤：</p>
        <ol>
            <li>在微信环境中打开此页面</li>
            <li>观察扫码按钮初始状态（应该是禁用的，因为用户未登录）</li>
            <li>点击"模拟登录"按钮</li>
            <li>观察扫码按钮是否变为可用状态</li>
            <li>点击"模拟登出"按钮</li>
            <li>观察扫码按钮是否变为禁用状态</li>
        </ol>
    </div>

    <div class="test-section">
        <h2>当前状态</h2>
        <div id="status-display">
            <div class="status info">
                <strong>微信环境：</strong><span id="wechat-env">检测中...</span>
            </div>
            <div class="status info">
                <strong>登录状态：</strong><span id="login-status">检测中...</span>
            </div>
            <div class="status info">
                <strong>微信SDK状态：</strong><span id="sdk-status">检测中...</span>
            </div>
            <div class="status info">
                <strong>扫码可用性：</strong><span id="scan-available">检测中...</span>
            </div>
        </div>
    </div>

    <div class="test-section">
        <h2>操作按钮</h2>
        <button id="simulate-login" class="primary">模拟登录</button>
        <button id="simulate-logout" class="primary">模拟登出</button>
        <button id="refresh-status" class="success">刷新状态</button>
    </div>

    <div class="test-section">
        <h2>扫码按钮测试</h2>
        <div id="scan-button-container">
            <!-- 这里会动态插入扫码按钮 -->
        </div>
    </div>

    <script>
        // 模拟微信环境检测
        function isWechatBrowser() {
            const ua = navigator.userAgent.toLowerCase();
            return /micromessenger/.test(ua);
        }

        // 模拟登录状态检测
        function isUserLoggedIn() {
            // 检查localStorage中的token
            const gucStore = localStorage.getItem('guc-store');
            if (!gucStore) return false;
            
            try {
                const store = JSON.parse(gucStore);
                return !!(store.token);
            } catch (e) {
                return false;
            }
        }

        // 更新状态显示
        function updateStatus() {
            document.getElementById('wechat-env').textContent = isWechatBrowser() ? '是' : '否';
            document.getElementById('login-status').textContent = isUserLoggedIn() ? '已登录' : '未登录';
            
            // 检查微信SDK状态（如果在微信环境中）
            if (isWechatBrowser() && window.wx) {
                document.getElementById('sdk-status').textContent = '已加载';
            } else {
                document.getElementById('sdk-status').textContent = '未加载';
            }
            
            // 检查扫码可用性
            const canScan = isWechatBrowser() && isUserLoggedIn();
            document.getElementById('scan-available').textContent = canScan ? '可用' : '不可用';
        }

        // 模拟登录
        function simulateLogin() {
            const mockToken = 'mock-token-' + Date.now();
            const gucStore = {
                token: mockToken,
                refreshToken: 'mock-refresh-token'
            };
            localStorage.setItem('guc-store', JSON.stringify(gucStore));
            
            // 模拟用户信息
            const mockUser = {
                id: '123456',
                name: '测试用户',
                orgList: [{ name: '测试组织' }]
            };
            
            // 触发状态更新
            updateStatus();
            alert('模拟登录成功！');
        }

        // 模拟登出
        function simulateLogout() {
            localStorage.removeItem('guc-store');
            updateStatus();
            alert('模拟登出成功！');
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateStatus();
            
            // 绑定事件
            document.getElementById('simulate-login').addEventListener('click', simulateLogin);
            document.getElementById('simulate-logout').addEventListener('click', simulateLogout);
            document.getElementById('refresh-status').addEventListener('click', updateStatus);
            
            // 定期更新状态
            setInterval(updateStatus, 2000);
        });
    </script>
</body>
</html>
