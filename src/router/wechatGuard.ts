/**
 * 微信SDK路由守卫
 * 确保在页面切换时微信SDK状态正确
 */
import type { Router } from 'vue-router'
import { useWechatStore } from '@/store/wechat'
import { isWechatBrowser } from '@/utils/wechat'
import { isUserLoggedIn } from '@/components/QrScanner/wechat'

export function createWechatGuard(router: Router) {
  router.beforeEach(async (_to, _from, next) => {
    // 只在微信环境中处理
    if (!isWechatBrowser()) {
      next()
      return
    }

    // 只有在用户已登录的情况下才处理微信SDK
    if (!isUserLoggedIn()) {
      console.log('用户未登录，跳过微信SDK路由守卫处理')
      next()
      return
    }

    try {
      const wechatStore = useWechatStore()

      // 检查并重新初始化微信SDK（如果URL变化）
      await wechatStore.checkAndReinitialize()

      next()
    } catch (error) {
      console.warn('微信SDK路由守卫处理失败:', error)
      // 即使失败也继续导航，不阻塞用户操作
      next()
    }
  })
}
