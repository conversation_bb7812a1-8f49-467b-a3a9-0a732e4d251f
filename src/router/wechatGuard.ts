/**
 * 微信SDK路由守卫（简化版）
 * 现在微信SDK初始化已经简化到组件内部，这里只做基本的环境检查
 */
import type { Router } from 'vue-router'
import { isWechatBrowser } from '@/utils/wechat'

export function createWechatGuard(router: Router) {
  router.beforeEach(async (_to, _from, next) => {
    // 只在微信环境中记录日志
    if (isWechatBrowser()) {
      console.log('微信环境路由切换:', _from?.path, '->', _to.path)
    }

    // 直接继续导航，不阻塞
    next()
  })
}
