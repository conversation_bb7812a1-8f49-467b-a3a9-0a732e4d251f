# 微信SDK认证修复说明

## 问题描述

之前的代码在用户未登录时仍然会尝试初始化微信SDK，这会导致以下问题：
1. 调用需要认证的API `V1OpenApiUserLoginWechatSignaturePost`
2. 可能导致401/403错误
3. 浪费网络资源和性能

## 解决方案

### 1. 添加用户登录状态检查函数

在 `src/components/QrScanner/wechat.ts` 中添加了 `isUserLoggedIn()` 函数：

```typescript
/**
 * 检查用户是否已登录
 * @returns 是否已登录
 */
export function isUserLoggedIn(): boolean {
  // 检查是否有token
  const token = gucStore.getToken()
  if (!token) {
    return false
  }

  // 检查用户信息是否存在
  const userStore = useCurrentUser()
  const userInfo = userStore.sysUserGet
  
  return !!(userInfo && userInfo.id)
}
```

### 2. 修改API调用函数

在 `getWxConfig()` 函数中添加登录状态检查：

```typescript
export async function getWxConfig(params: GetWxConfigParams): Promise<GetWxConfigResponse> {
  // 检查用户登录状态
  if (!isUserLoggedIn()) {
    throw new Error('用户未登录，无法获取微信配置')
  }
  // ... 其余代码
}
```

### 3. 修改SDK初始化函数

在 `initWechatSDK()` 函数中添加登录状态检查：

```typescript
export async function initWechatSDK(debug = false): Promise<WxConfig> {
  // 检查用户登录状态
  if (!isUserLoggedIn()) {
    throw new Error('用户未登录，无法初始化微信SDK')
  }
  // ... 其余代码
}
```

### 4. 修改微信Store

在 `src/store/wechat.ts` 的 `initializeWechatSDK()` 函数中添加检查：

```typescript
const initializeWechatSDK = async (force = false): Promise<boolean> => {
  // 如果不在微信环境中，直接返回
  if (!isWechatBrowser()) {
    console.log('不在微信环境中，跳过微信SDK初始化')
    return false
  }

  // 检查用户登录状态
  if (!isUserLoggedIn()) {
    console.log('用户未登录，跳过微信SDK初始化')
    return false
  }
  // ... 其余代码
}
```

### 5. 修改应用启动时的初始化

在 `src/modules/wechat.ts` 中添加登录状态检查：

```typescript
export default defineVuePlugin((app) => {
  // 在应用启动时初始化微信SDK
  if (typeof window !== 'undefined' && isWechatBrowser()) {
    // 延迟初始化，确保DOM已加载，并检查用户登录状态
    setTimeout(() => {
      // 只有在用户已登录的情况下才初始化微信SDK
      if (isUserLoggedIn()) {
        const wechatStore = useWechatStore()
        wechatStore.initializeWechatSDK().catch(error => {
          console.warn('微信SDK初始化失败:', error)
        })
      } else {
        console.log('用户未登录，跳过微信SDK自动初始化')
      }
    }, 100)
  }
})
```

### 6. 修改路由守卫

在 `src/router/wechatGuard.ts` 中添加登录状态检查：

```typescript
export function createWechatGuard(router: Router) {
  router.beforeEach(async (_to, _from, next) => {
    // 只在微信环境中处理
    if (!isWechatBrowser()) {
      next()
      return
    }

    // 只有在用户已登录的情况下才处理微信SDK
    if (!isUserLoggedIn()) {
      console.log('用户未登录，跳过微信SDK路由守卫处理')
      next()
      return
    }
    // ... 其余代码
  })
}
```

### 7. 修改QrScanner组件

在 `src/components/QrScanner/index.vue` 中修改组件挂载逻辑：

```typescript
// 组件挂载时初始化
onMounted(() => {
  // 在微信环境中且用户已登录时初始化微信SDK
  if (isInWechat.value && isUserLoggedIn()) {
    initWechat()
  } else if (isInWechat.value && !isUserLoggedIn()) {
    console.log('用户未登录，跳过微信SDK初始化')
  }
})
```

## 修改效果

1. **防止未登录时的API调用**: 现在只有在用户已登录时才会调用微信配置API
2. **提升性能**: 避免了不必要的网络请求和SDK初始化
3. **更好的错误处理**: 提供明确的错误信息，便于调试
4. **保持功能完整性**: 用户登录后微信SDK功能正常工作

## 测试建议

1. 在未登录状态下访问包含QrScanner组件的页面，确认不会调用微信API
2. 登录后确认微信SDK正常初始化和工作
3. 检查浏览器控制台，确认有适当的日志输出
4. 测试路由切换时的行为

## 注意事项

- 这个修改是向后兼容的，不会影响现有的功能
- 如果需要在特定场景下强制初始化微信SDK，可以考虑添加额外的参数或配置
- 建议在生产环境部署前进行充分测试

## 构建验证

✅ 代码修改已通过构建验证，没有语法错误或类型错误

## 修改的文件列表

1. `src/components/QrScanner/wechat.ts` - 添加登录状态检查函数和API调用保护
2. `src/store/wechat.ts` - 修改微信SDK状态管理，添加登录检查
3. `src/modules/wechat.ts` - 修改应用启动时的微信SDK初始化逻辑
4. `src/router/wechatGuard.ts` - 修改路由守卫，添加登录状态检查
5. `src/components/QrScanner/index.vue` - 修改组件挂载时的初始化逻辑
6. `src/components/QrScanner/__tests__/wechat.test.ts` - 添加单元测试（新增）

## 核心改进

这次修改的核心是在所有微信SDK相关的初始化点添加了用户登录状态检查，确保：

1. **API安全性**: 只有登录用户才能调用需要认证的微信配置API
2. **性能优化**: 避免未登录用户的无效网络请求
3. **错误处理**: 提供清晰的错误信息和日志
4. **用户体验**: 登录后微信功能正常工作，未登录时优雅降级
