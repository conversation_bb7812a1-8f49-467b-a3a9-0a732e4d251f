import type { WxConfig } from '@/utils/wechat'
/**
 * 微信相关API接口
 */
import { V1OpenApiUserLoginWechatSignaturePost } from '@/api/api.req'
import { DEFAULT_JS_API_LIST } from '@/config/wechat'
import { gucStore } from '@/integration/guc'
import { useCurrentUser } from '@/store/sysUser'

// 获取微信JS-SDK配置的请求参数
export interface GetWxConfigParams {
  url: string // 当前页面的URL，不包含#及其后面部分
}

// 获取微信JS-SDK配置的响应数据
export interface GetWxConfigResponse {
  appId: string
  timestamp: string
  nonceStr: string
  signature: string
  jsApiList: string[]
}

/**
 * 检查用户是否已登录
 * @returns 是否已登录
 */
export function isUserLoggedIn(): boolean {
  // 检查是否有token
  const token = gucStore.getToken()
  if (!token) {
    return false
  }

  // 检查用户信息是否存在
  const userStore = useCurrentUser()
  const userInfo = userStore.sysUserGet

  return !!(userInfo && userInfo.id)
}

/**
 * 获取微信JS-SDK配置信息
 * @param params 请求参数
 * @returns 微信配置信息
 */
export async function getWxConfig(params: GetWxConfigParams): Promise<GetWxConfigResponse> {
  // 检查用户登录状态
  if (!isUserLoggedIn()) {
    throw new Error('用户未登录，无法获取微信配置')
  }

  try {
    const response = await V1OpenApiUserLoginWechatSignaturePost({ url: params.url })

    if (!response) {
      throw new Error('获取微信配置失败：响应数据为空')
    }

    return {
      appId: response.appid || '',
      timestamp: response.timestamp || '',
      nonceStr: response.nonceStr || '',
      signature: response.signature || '',
      jsApiList: DEFAULT_JS_API_LIST, // 使用默认的jsApiList
    }
  }
  catch (error) {
    console.error('获取微信配置失败:', error)
    throw new Error('获取微信配置失败，请稍后重试')
  }
}

/**
 * 获取当前页面URL（用于微信JS-SDK配置）
 * @returns 当前页面URL
 */
export function getCurrentPageUrl(): string {
  // 获取当前页面的完整URL，但不包含hash部分
  const url = window.location.href.split('#')[0]
  return url
}

/**
 * 构建微信JS-SDK配置对象
 * @param configData 从后端获取的配置数据
 * @param debug 是否开启调试模式
 * @returns 微信配置对象
 */
export function buildWxConfig(configData: GetWxConfigResponse, debug = false): WxConfig {
  return {
    debug,
    appId: configData.appId, // 使用 appId 作为 appId
    timestamp: Number.parseInt(configData.timestamp) || Date.now(), // 转换为数字类型
    nonceStr: configData.nonceStr,
    signature: configData.signature,
    jsApiList: configData.jsApiList || DEFAULT_JS_API_LIST,
  }
}

/**
 * 初始化微信JS-SDK
 * @param debug 是否开启调试模式
 * @returns Promise<WxConfig>
 */
export async function initWechatSDK(debug = false): Promise<WxConfig> {
  // 检查用户登录状态
  if (!isUserLoggedIn()) {
    throw new Error('用户未登录，无法初始化微信SDK')
  }

  try {
    // 获取当前页面URL
    const url = getCurrentPageUrl()

    // 从后端获取微信配置
    const configData = await getWxConfig({ url })

    // 构建配置对象
    const wxConfig = buildWxConfig(configData, debug)
    console.log('wxConfig', wxConfig)

    return wxConfig
  }
  catch (error) {
    console.error('初始化微信SDK失败:', error)
    throw error
  }
}
